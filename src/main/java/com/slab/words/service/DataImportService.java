package com.slab.words.service;

import com.slab.words.domain.Word;
import com.slab.words.domain.WordBook;
import com.slab.words.domain.enums.Stage;
import com.slab.words.repository.WordBookRepository;
import com.slab.words.repository.WordRepository;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataImportService {

    private final WordBookRepository wordBookRepository;
    private final WordRepository wordRepository;

    private final AtomicInteger totalWords = new AtomicInteger(0);
    private final AtomicInteger processedWords = new AtomicInteger(0);
    private final AtomicLong startTime = new AtomicLong(0);
    private volatile boolean isImporting = false;

    @Data
    public static class ImportProgress {
        private boolean isImporting;
        private int totalWords;
        private int processedWords;
        private long startTime;
        private double progress;
        private long elapsedTime;
        private long estimatedTimeRemaining;
    }

    @Transactional
    public String importWordBook(String name, String stage, String description, MultipartFile file) throws IOException {
        // 先导入前20条进行测试
        return importWordBookInternal(name, stage, description, file, 20);
    }

    @Transactional
    public String importWordBookBatch(String name, String stage, String description, MultipartFile file)
            throws IOException {
        // 导入所有数据
        return importWordBookInternal(name, stage, description, file, -1);
    }

    private String importWordBookInternal(String name, String stage, String description, MultipartFile file, int limit)
            throws IOException {
        if (isImporting) {
            throw new IllegalStateException("已有导入任务正在进行中");
        }

        try {
            isImporting = true;
            startTime.set(System.currentTimeMillis());
            totalWords.set(0);
            processedWords.set(0);

            // 解析文件获取总单词数
            List<String> lines = readFileLines(file);
            List<WordData> wordDataList = parseWordData(lines);

            if (limit > 0) {
                wordDataList = wordDataList.subList(0, Math.min(limit, wordDataList.size()));
            }

            totalWords.set(wordDataList.size());

            // 创建词书
            WordBook wordBook = WordBook.builder()
                    .name(name)
                    .stage(Stage.valueOf(stage.toUpperCase()))
                    .description(description)
                    .totalWords(wordDataList.size())
                    .language("en")
                    .build();

            wordBook = wordBookRepository.save(wordBook);
            log.info("创建词书: {} (ID: {})", name, wordBook.getId());

            // 导入单词
            List<Word> words = new ArrayList<>();
            for (int i = 0; i < wordDataList.size(); i++) {
                WordData wordData = wordDataList.get(i);
                Word word = Word.builder()
                        .bookId(wordBook.getId())
                        .headword(wordData.word)
                        .phonetic(wordData.phonetic)
                        .pos(wordData.pos)
                        .meaning(wordData.meaning)
                        .build();
                words.add(word);

                processedWords.incrementAndGet();

                // 每100个单词保存一次
                if (words.size() >= 100) {
                    wordRepository.saveAll(words);
                    words.clear();
                    log.info("已处理 {}/{} 个单词", processedWords.get(), totalWords.get());
                }
            }

            // 保存剩余的单词
            if (!words.isEmpty()) {
                wordRepository.saveAll(words);
            }

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime.get();

            String result = String.format("成功导入词书 '%s'，共 %d 个单词，耗时 %d 毫秒",
                    name, totalWords.get(), duration);

            log.info(result);
            return result;

        } finally {
            isImporting = false;
        }
    }

    private List<String> readFileLines(MultipartFile file) throws IOException {
        List<String> lines = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.trim().isEmpty()) {
                    lines.add(line.trim());
                }
            }
        }
        return lines;
    }

    private List<WordData> parseWordData(List<String> lines) {
        List<WordData> wordDataList = new ArrayList<>();

        for (String line : lines) {
            // 跳过标题行和空行
            if (line.startsWith("大学英语") || line.startsWith("共") || line.startsWith("(")) {
                continue;
            }

            WordData wordData = parseLine(line);
            if (wordData != null) {
                wordDataList.add(wordData);
            }
        }

        return wordDataList;
    }

    private WordData parseLine(String line) {
        try {
            // 处理四级格式: abandon [əˈbændən] vt.丢弃；放弃，抛弃
            if (line.contains("[") && line.contains("]")) {
                int bracketStart = line.indexOf('[');
                int bracketEnd = line.indexOf(']');

                if (bracketStart > 0 && bracketEnd > bracketStart) {
                    String word = line.substring(0, bracketStart).trim();
                    String phonetic = line.substring(bracketStart + 1, bracketEnd);
                    String remaining = line.substring(bracketEnd + 1).trim();

                    // 提取词性和释义
                    String[] parts = remaining.split("\\s+", 2);
                    String pos = parts[0];
                    String meaning = parts.length > 1 ? parts[1] : "";

                    return new WordData(word, phonetic, pos, meaning);
                }
            }

            // 处理六级格式: consistent adj. 一致的
            if (line.contains("\t")) {
                String[] parts = line.split("\t");
                if (parts.length >= 2) {
                    String word = parts[0].trim();
                    String posAndMeaning = parts[1].trim();

                    // 提取词性和释义
                    String[] posParts = posAndMeaning.split("\\s+", 2);
                    String pos = posParts[0];
                    String meaning = posParts.length > 1 ? posParts[1] : "";

                    return new WordData(word, "", pos, meaning);
                }
            }

            return null;
        } catch (Exception e) {
            log.warn("解析行失败: {}", line, e);
            return null;
        }
    }

    public ImportProgress getProgress() {
        ImportProgress progress = new ImportProgress();
        progress.setImporting(isImporting);
        progress.setTotalWords(totalWords.get());
        progress.setProcessedWords(processedWords.get());
        progress.setStartTime(startTime.get());

        if (totalWords.get() > 0) {
            progress.setProgress((double) processedWords.get() / totalWords.get());
        }

        if (startTime.get() > 0) {
            long currentTime = System.currentTimeMillis();
            progress.setElapsedTime(currentTime - startTime.get());

            if (processedWords.get() > 0 && isImporting) {
                long estimatedTotalTime = (currentTime - startTime.get()) * totalWords.get() / processedWords.get();
                progress.setEstimatedTimeRemaining(estimatedTotalTime - progress.getElapsedTime());
            }
        }

        return progress;
    }

    @Transactional
    public String deleteWordBook(String name) {
        // 查找词书
        WordBook wordBook = wordBookRepository.findByName(name)
                .orElseThrow(() -> new IllegalArgumentException("词书不存在: " + name));

        log.info("删除词书: {} (ID: {})", name, wordBook.getId());

        // 删除词书（会级联删除所有单词）
        wordBookRepository.delete(wordBook);

        return String.format("成功删除词书 '%s' 及其所有单词", name);
    }

    @Data
    private static class WordData {
        private final String word;
        private final String phonetic;
        private final String pos;
        private final String meaning;
    }
}
