package com.slab.words.domain;

import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "dictionary_entries")
public class DictionaryEntry {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 128)
    private String word;

    @Column(nullable = false, length = 128)
    private String sw; // strip-word for fuzzy matching

    @Column(length = 64)
    private String phonetic;

    @Column(columnDefinition = "MEDIUMTEXT")
    private String definition;

    @Column(columnDefinition = "MEDIUMTEXT")
    private String translation;

    @Column(length = 128)
    private String pos;

    private Integer collins; // star level

    private Integer oxford; // 0/1

    @Column(length = 512)
    private String tag;

    private Integer bnc;

    private Integer frq;

    @Column(columnDefinition = "TEXT")
    private String exchange;

    @Column(columnDefinition = "json")
    private String detail;

    @Column(name = "audio_url", length = 255)
    private String audioUrl;

    @Column(name = "created_at", insertable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", insertable = false, updatable = false)
    private LocalDateTime updatedAt;
}
