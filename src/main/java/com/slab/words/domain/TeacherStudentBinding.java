package com.slab.words.domain;

import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "teacher_student_bindings", indexes = {
        @Index(name = "idx_bindings_teacher", columnList = "teacher_id"),
        @Index(name = "idx_bindings_student", columnList = "student_id")
}, uniqueConstraints = {
        @UniqueConstraint(name = "uq_binding_teacher_student", columnNames = {"teacher_id", "student_id"}),
        @UniqueConstraint(name = "uq_binding_student_unique", columnNames = {"student_id"})
})
public class TeacherStudentBinding {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "teacher_id", nullable = false)
    private Long teacherId;

    @Column(name = "student_id", nullable = false)
    private Long studentId;

    @Column(name = "created_at", insertable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", insertable = false, updatable = false)
    private LocalDateTime updatedAt;
}
