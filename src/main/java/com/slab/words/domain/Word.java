package com.slab.words.domain;

import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "words")
public class Word {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "book_id", nullable = false)
    private Long bookId;

    @Column(nullable = false, length = 128)
    private String headword;

    @Column(length = 64)
    private String phonetic;

    @Column(length = 16)
    private String pos;

    @Column(columnDefinition = "json")
    private String meaning;

    @Column(name = "audio_url", length = 255)
    private String audioUrl;

    private Integer frequency;

    @Column(name = "dict_id")
    private Long dictId; // 可选：关联到 dictionary_entries.id

    @Column(name = "created_at", insertable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", insertable = false, updatable = false)
    private LocalDateTime updatedAt;
}
