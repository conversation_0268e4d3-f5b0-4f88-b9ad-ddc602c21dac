package com.slab.words.domain;

import com.slab.words.domain.enums.Stage;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "word_books")
public class WordBook {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 100)
    private String name;

    @Column(nullable = false, length = 20)
    private String language = "en";

    @Enumerated(EnumType.STRING)
    @Column(length = 16)
    private Stage stage;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(name = "total_words")
    private Integer totalWords;

    @Column(name = "created_at", insertable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", insertable = false, updatable = false)
    private LocalDateTime updatedAt;
}
