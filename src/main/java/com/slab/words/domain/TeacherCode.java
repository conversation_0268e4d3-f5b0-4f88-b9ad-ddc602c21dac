package com.slab.words.domain;

import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "teacher_codes", indexes = {
        @Index(name = "idx_teacher_codes_teacher", columnList = "teacher_id")
}, uniqueConstraints = {
        @UniqueConstraint(name = "uq_teacher_codes_teacher", columnNames = {"teacher_id"}),
        @UniqueConstraint(name = "uq_teacher_codes_code", columnNames = {"code"})
})
public class TeacherCode {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "teacher_id", nullable = false)
    private Long teacherId;

    @Column(name = "code", nullable = false, length = 16)
    private String code;

    @Column(name = "rotated_at")
    private LocalDateTime rotatedAt;

    @Column(name = "created_at", insertable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", insertable = false, updatable = false)
    private LocalDateTime updatedAt;
}
