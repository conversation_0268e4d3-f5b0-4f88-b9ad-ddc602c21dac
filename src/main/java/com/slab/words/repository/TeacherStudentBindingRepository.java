package com.slab.words.repository;

import com.slab.words.domain.TeacherStudentBinding;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface TeacherStudentBindingRepository extends JpaRepository<TeacherStudentBinding, Long> {
    Page<TeacherStudentBinding> findByTeacherId(Long teacherId, Pageable pageable);
    Page<TeacherStudentBinding> findByTeacherIdAndStudentIdIn(Long teacherId, List<Long> studentIds, Pageable pageable);
    Optional<TeacherStudentBinding> findByStudentId(Long studentId);
    void deleteByTeacherIdAndStudentId(Long teacherId, Long studentId);
    boolean existsByTeacherIdAndStudentId(Long teacherId, Long studentId);
    boolean existsByStudentId(Long studentId);
}
