package com.slab.words.repository;

import com.slab.words.domain.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;
import java.util.List;

public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByUsername(String username);
    List<User> findByIdIn(List<Long> ids);
    List<User> findByIdInAndUsernameContainingIgnoreCase(List<Long> ids, String username);

    @Query("select u from User u where u.id in :ids and ( lower(u.username) like lower(concat('%', :keyword, '%')) or lower(coalesce(u.realName, '')) like lower(concat('%', :keyword, '%')) )")
    List<User> searchByIdsAndNameLike(@Param("ids") List<Long> ids, @Param("keyword") String keyword);
}
