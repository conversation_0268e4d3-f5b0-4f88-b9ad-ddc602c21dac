package com.slab.words.repository;

import com.slab.words.domain.Word;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

public interface WordRepository extends JpaRepository<Word, Long> {
    Page<Word> findByBookId(Long bookId, Pageable pageable);
    Page<Word> findByBookIdAndHeadwordContainingIgnoreCase(Long bookId, String headword, Pageable pageable);
}
