package com.slab.words.repository;

import com.slab.words.domain.DictionaryEntry;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface DictionaryEntryRepository extends JpaRepository<DictionaryEntry, Long> {
    Optional<DictionaryEntry> findByWordIgnoreCase(String word);

    Page<DictionaryEntry> findByWordStartingWithIgnoreCase(String prefix, Pageable pageable);

    Page<DictionaryEntry> findByWordContainingIgnoreCase(String part, Pageable pageable);

    Page<DictionaryEntry> findBySwStartingWith(String sw, Pageable pageable);
}
