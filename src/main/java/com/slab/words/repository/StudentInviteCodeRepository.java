package com.slab.words.repository;

import com.slab.words.domain.StudentInviteCode;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface StudentInviteCodeRepository extends JpaRepository<StudentInviteCode, Long> {
    Optional<StudentInviteCode> findByCode(String code);
    Optional<StudentInviteCode> findByStudentId(Long studentId);
    // 学生通过已登录账号绑定时，usedBy 记录的是“真实学生账号ID”，用于反查激活状态和展示名
    Optional<StudentInviteCode> findTopByUsedByOrderByUsedAtDesc(Long usedBy);
    boolean existsByCode(String code);
    void deleteByStudentId(Long studentId);
}
