package com.slab.words.repository;

import com.slab.words.domain.WordBook;
import com.slab.words.domain.enums.Stage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

public interface WordBookRepository extends JpaRepository<WordBook, Long> {
    Page<WordBook> findByNameContainingIgnoreCase(String name, Pageable pageable);

    Page<WordBook> findByStage(Stage stage, Pageable pageable);

    Page<WordBook> findByStageAndNameContainingIgnoreCase(Stage stage, String name, Pageable pageable);
}
