package com.slab.words.repository;

import com.slab.words.domain.StudentBookEnrollment;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface StudentBookEnrollmentRepository extends JpaRepository<StudentBookEnrollment, Long> {
    Optional<StudentBookEnrollment> findByStudentIdAndBookId(Long studentId, Long bookId);
    List<StudentBookEnrollment> findByStudentId(Long studentId);
    Optional<StudentBookEnrollment> findTopByStudentIdOrderByCreatedAtDesc(Long studentId);
}
