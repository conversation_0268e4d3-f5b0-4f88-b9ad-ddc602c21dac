package com.slab.words.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class StudentInviteActivateRequest {
    @NotBlank
    private String code;

    @NotBlank
    @Size(min = 3, max = 50)
    private String username;

    @NotBlank
    @Size(min = 6, max = 64)
    private String password;

    @Size(min = 0, max = 50)
    private String realName;
}
