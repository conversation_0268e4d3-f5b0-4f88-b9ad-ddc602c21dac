package com.slab.words.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageResponse<T> {
    private long totalElements;
    private int totalPages;
    private int page;
    private int size;
    private List<T> content;

    public static <T> PageResponse<T> from(Page<T> page) {
        return new PageResponse<>(
                page.getTotalElements(),
                page.getTotalPages(),
                page.getNumber(),
                page.getSize(),
                page.getContent()
        );
    }
    
    public static <T> PageResponse<T> empty(int page, int size) {
        return new PageResponse<>(0, 0, page, size, List.of());
    }

    public static <T> PageResponse<T> ofSingle(T item, int page, int size) {
        return new PageResponse<>(1, 1, page, size, List.of(item));
    }
}
