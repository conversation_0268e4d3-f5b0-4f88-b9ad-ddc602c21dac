package com.slab.words.dto;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class LessonPlanUpsertRequest {
    @NotNull
    private Long studentId;
    @NotNull
    private Long bookId;

    @Min(1)
    private Integer dailyNewWords = 20;

    @Min(0)
    private Integer dailyReviewWords = 50;

    // JSON 字符串，前端可直接传
    private String ebbinghausPlan;
}
