package com.slab.words.dto;

import com.slab.words.domain.enums.Role;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuthResponse {
    private String token;
    private String tokenType;
    private long expiresInSeconds;
    private String username;
    private Role role;
    private String realName;
}
