package com.slab.words.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StudentSummary {
    private Long id;             // 学生用户ID
    private String name;         // 学生姓名（用户名）
    private String studentId;    // 学号（暂无专门字段，先用ID字符串或用户名占位）
    private String wordbook;     // 当前词书名称（阶段1占位）
    private Integer progress;    // 学习进度0-100（阶段1占位）
    private Integer completedLessons; // 已完成课时（阶段1占位）
    private Integer totalLessons;     // 总课时（阶段1占位）
    private String lastActive;   // 最近活跃文案（阶段1占位）
    private Boolean activated;   // 是否已激活（使用激活码完成绑定）
}
