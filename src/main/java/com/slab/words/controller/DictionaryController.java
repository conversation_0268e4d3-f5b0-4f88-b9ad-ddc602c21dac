package com.slab.words.controller;

import com.slab.words.common.ApiResponse;
import com.slab.words.domain.DictionaryEntry;
import com.slab.words.dto.PageResponse;
import com.slab.words.repository.DictionaryEntryRepository;
import jakarta.validation.constraints.Min;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

@RestController
@RequestMapping("/dict/words")
@Validated
public class DictionaryController {

    private final DictionaryEntryRepository repo;

    public DictionaryController(DictionaryEntryRepository repo) {
        this.repo = repo;
    }

    @GetMapping
    public ApiResponse<PageResponse<DictionaryEntry>> list(
            @RequestParam(value = "q", required = false) String q,
            @RequestParam(value = "mode", defaultValue = "prefix") String mode,
            @RequestParam(value = "page", defaultValue = "0") @Min(0) int page,
            @RequestParam(value = "size", defaultValue = "20") @Min(1) int size
    ) {
        if (q == null || q.isBlank()) {
            // 避免无条件扫描海量数据：q 为空返回空分页
            return ApiResponse.success(PageResponse.empty(page, size));
        }
        PageRequest pr = PageRequest.of(page, size);
        Page<DictionaryEntry> result;
        switch (mode.toLowerCase()) {
            case "exact":
                var one = repo.findByWordIgnoreCase(q).map(x -> PageResponse.ofSingle(x, page, size));
                return ApiResponse.success(one.orElse(PageResponse.empty(page, size)));
            case "contains":
                result = repo.findByWordContainingIgnoreCase(q, pr);
                break;
            case "sw":
                result = repo.findBySwStartingWith(stripWord(q), pr);
                break;
            case "prefix":
            default:
                result = repo.findByWordStartingWithIgnoreCase(q, pr);
        }
        return ApiResponse.success(PageResponse.from(result));
    }

    @GetMapping("/{id}")
    public ApiResponse<DictionaryEntry> getById(@PathVariable("id") Long id) {
        var entry = repo.findById(id).orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Not found"));
        return ApiResponse.success(entry);
    }

    @GetMapping("/by-head/{word}")
    public ApiResponse<DictionaryEntry> getByHead(@PathVariable("word") String word) {
        var entry = repo.findByWordIgnoreCase(word).orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Not found"));
        return ApiResponse.success(entry);
    }

    private String stripWord(String word) {
        if (word == null) return "";
        StringBuilder sb = new StringBuilder();
        for (char c : word.toCharArray()) {
            if (Character.isLetterOrDigit(c)) {
                sb.append(Character.toLowerCase(c));
            }
        }
        return sb.toString();
    }
}
