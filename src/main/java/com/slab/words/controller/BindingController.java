package com.slab.words.controller;

import com.slab.words.common.ApiResponse;
import com.slab.words.domain.TeacherStudentBinding;
import com.slab.words.domain.User;
import com.slab.words.dto.UnbindRequest;
import com.slab.words.dto.BindRequest;
import com.slab.words.dto.StudentInviteActivateRequest;
import com.slab.words.dto.AuthResponse;
import com.slab.words.repository.TeacherCodeRepository;
import com.slab.words.repository.TeacherStudentBindingRepository;
import com.slab.words.repository.UserRepository;
import com.slab.words.repository.StudentInviteCodeRepository;
import com.slab.words.security.JwtUtil;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/bindings")
public class BindingController {

    private final TeacherStudentBindingRepository bindingRepository;
    private final UserRepository userRepository;
    private final TeacherCodeRepository teacherCodeRepository;
    private final StudentInviteCodeRepository studentInviteCodeRepository;
    private final JwtUtil jwtUtil;
    private final PasswordEncoder passwordEncoder;

    @Value("${app.security.jwt.access-token-exp-minutes:30}")
    private long accessTokenExpMinutes;

    public BindingController(TeacherStudentBindingRepository bindingRepository,
                             UserRepository userRepository,
                             TeacherCodeRepository teacherCodeRepository,
                             StudentInviteCodeRepository studentInviteCodeRepository,
                             JwtUtil jwtUtil,
                             PasswordEncoder passwordEncoder) {
        this.bindingRepository = bindingRepository;
        this.userRepository = userRepository;
        this.teacherCodeRepository = teacherCodeRepository;
        this.studentInviteCodeRepository = studentInviteCodeRepository;
        this.jwtUtil = jwtUtil;
        this.passwordEncoder = passwordEncoder;
    }

    // 教师解绑学生
    @PreAuthorize("hasRole('TEACHER')")
    @PostMapping("/unbind")
    @Transactional
    public ApiResponse<Void> unbind(@AuthenticationPrincipal UserDetails principal,
                                    @Valid @RequestBody UnbindRequest req) {
        if (principal == null) return ApiResponse.failure(HttpStatus.UNAUTHORIZED.value(), "Unauthorized", null);
        User me = userRepository.findByUsername(principal.getUsername()).orElseThrow();
        if (bindingRepository.existsByTeacherIdAndStudentId(me.getId(), req.getStudentId())) {
            // 如该预建学生的邀请码已被“已登录学生”使用，先删除该“已登录学生”的绑定关系，避免教师列表出现第二张卡片
            var sicOpt = studentInviteCodeRepository.findByStudentId(req.getStudentId());
            if (sicOpt.isPresent()) {
                var sic = sicOpt.get();
                Long usedBy = sic.getUsedBy();
                if (sic.isUsed() && usedBy != null) {
                    // 删除与已登录学生的绑定，仅保留预建学生的绑定
                    if (bindingRepository.existsByTeacherIdAndStudentId(me.getId(), usedBy)) {
                        bindingRepository.deleteByTeacherIdAndStudentId(me.getId(), usedBy);
                    }
                }
                // 重置激活码为未使用状态
                sic.setUsed(false);
                sic.setUsedBy(null);
                sic.setUsedAt(null);
                studentInviteCodeRepository.save(sic);
            }
        }
        return ApiResponse.success(null);
    }

    // 学生使用一次性激活码激活预建账号并登录（匿名可访问）
    @PostMapping("/activate")
    @Transactional
    public ApiResponse<AuthResponse> activate(@Valid @RequestBody StudentInviteActivateRequest req) {
        if (req.getCode() == null || req.getCode().isBlank()) {
            return ApiResponse.failure(HttpStatus.BAD_REQUEST.value(), "Invalid code", null);
        }
        var inviteOpt = studentInviteCodeRepository.findByCode(req.getCode().trim());
        if (inviteOpt.isEmpty()) {
            return ApiResponse.failure(HttpStatus.NOT_FOUND.value(), "Invite code not found", null);
        }
        var invite = inviteOpt.get();
        if (invite.isUsed()) {
            return ApiResponse.failure(HttpStatus.CONFLICT.value(), "Invite code already used", null);
        }
        var existingUserOpt = userRepository.findByUsername(req.getUsername().trim());
        if (existingUserOpt.isPresent() && !existingUserOpt.get().getId().equals(invite.getStudentId())) {
            return ApiResponse.failure(HttpStatus.CONFLICT.value(), "Username already exists", null);
        }
        var student = userRepository.findById(invite.getStudentId()).orElseThrow();
        student.setUsername(req.getUsername().trim());
        student.setPasswordHash(passwordEncoder.encode(req.getPassword()));
        if (req.getRealName() != null && !req.getRealName().isBlank()) {
            student.setRealName(req.getRealName().trim());
        }
        userRepository.save(student);

        invite.setUsed(true);
        invite.setUsedBy(student.getId());
        invite.setUsedAt(LocalDateTime.now());
        studentInviteCodeRepository.save(invite);

        if (!bindingRepository.existsByTeacherIdAndStudentId(invite.getTeacherId(), student.getId())) {
            TeacherStudentBinding b = TeacherStudentBinding.builder()
                    .teacherId(invite.getTeacherId())
                    .studentId(student.getId())
                    .build();
            bindingRepository.save(b);
        }

        Map<String, Object> claims = new HashMap<>();
        claims.put("role", student.getRole().name());
        claims.put("uid", student.getId());
        String token = jwtUtil.generateToken(student.getUsername(), claims);
        AuthResponse resp = new AuthResponse(token, "Bearer", accessTokenExpMinutes * 60, student.getUsername(), student.getRole(), student.getRealName());
        return ApiResponse.success(resp);
    }

    // 学生通过激活码绑定（已登录用户）
    @PostMapping("/bind-with-invite")
    @PreAuthorize("hasRole('STUDENT')")
    public ApiResponse<String> bindWithInviteCode(@AuthenticationPrincipal UserDetails principal, @RequestBody Map<String, String> req) {
        String inviteCode = req.get("inviteCode");
        if (inviteCode == null || inviteCode.isBlank()) {
            return ApiResponse.failure(HttpStatus.BAD_REQUEST.value(), "Invite code is required", null);
        }
        
        var inviteOpt = studentInviteCodeRepository.findByCode(inviteCode.trim());
        if (inviteOpt.isEmpty()) {
            return ApiResponse.failure(HttpStatus.NOT_FOUND.value(), "Invalid invite code", null);
        }
        
        var invite = inviteOpt.get();
        if (invite.isUsed()) {
            return ApiResponse.failure(HttpStatus.CONFLICT.value(), "Invite code already used", null);
        }
        
        User student = userRepository.findByUsername(principal.getUsername()).orElseThrow();
        
        // 检查是否已绑定其他老师
        if (bindingRepository.existsByStudentId(student.getId())) {
            return ApiResponse.failure(HttpStatus.CONFLICT.value(), "Student already bound to a teacher", null);
        }
        
        // 标记激活码已使用
        invite.setUsed(true);
        invite.setUsedBy(student.getId());
        invite.setUsedAt(LocalDateTime.now());
        studentInviteCodeRepository.save(invite);
        
        // 创建绑定关系
        if (!bindingRepository.existsByTeacherIdAndStudentId(invite.getTeacherId(), student.getId())) {
            TeacherStudentBinding b = TeacherStudentBinding.builder()
                    .teacherId(invite.getTeacherId())
                    .studentId(student.getId())
                    .build();
            bindingRepository.save(b);
        }
        
        return ApiResponse.success("Binding successful");
    }

    // 学生检查绑定状态
    @GetMapping("/status")
    @PreAuthorize("hasRole('STUDENT')")
    public ApiResponse<Map<String, Object>> getBindingStatus(@AuthenticationPrincipal UserDetails principal) {
        User student = userRepository.findByUsername(principal.getUsername()).orElseThrow();
        
        var bindingOpt = bindingRepository.findByStudentId(student.getId());
        Map<String, Object> result = new HashMap<>();
        
        if (bindingOpt.isPresent()) {
            var binding = bindingOpt.get();
            var teacher = userRepository.findById(binding.getTeacherId()).orElse(null);
            result.put("hasTeacher", true);
            result.put("teacherName", teacher != null ? teacher.getUsername() : "未知老师");
            result.put("teacherId", binding.getTeacherId());
        } else {
            result.put("hasTeacher", false);
            result.put("teacherName", "");
        }
        
        return ApiResponse.success(result);
    }

    // 学生通过教师码绑定
    @PreAuthorize("hasRole('STUDENT')")
    @PostMapping("/bind")
    public ApiResponse<Void> bind(@AuthenticationPrincipal UserDetails principal,
                                  @Valid @RequestBody BindRequest req) {
        if (principal == null) return ApiResponse.failure(HttpStatus.UNAUTHORIZED.value(), "Unauthorized", null);
        if (req.getCode() == null || req.getCode().isBlank()) {
            return ApiResponse.failure(HttpStatus.BAD_REQUEST.value(), "Invalid code", null);
        }
        User me = userRepository.findByUsername(principal.getUsername()).orElseThrow();
        var codeOpt = teacherCodeRepository.findByCode(req.getCode().trim());
        if (codeOpt.isEmpty()) {
            return ApiResponse.failure(HttpStatus.NOT_FOUND.value(), "Teacher code not found", null);
        }
        Long teacherId = codeOpt.get().getTeacherId();

        var existing = bindingRepository.findByStudentId(me.getId());
        if (existing.isPresent()) {
            TeacherStudentBinding b = existing.get();
            if (!teacherId.equals(b.getTeacherId())) {
                b.setTeacherId(teacherId);
                bindingRepository.save(b);
            }
        } else {
            TeacherStudentBinding b = TeacherStudentBinding.builder()
                    .teacherId(teacherId)
                    .studentId(me.getId())
                    .build();
            bindingRepository.save(b);
        }
        return ApiResponse.success(null);
    }
}
