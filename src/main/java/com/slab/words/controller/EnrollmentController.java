package com.slab.words.controller;

import com.slab.words.common.ApiResponse;
import com.slab.words.domain.StudentBookEnrollment;
import com.slab.words.dto.EnrollmentCreateRequest;
import com.slab.words.repository.StudentBookEnrollmentRepository;
import com.slab.words.repository.UserRepository;
import com.slab.words.repository.WordBookRepository;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/enrollments")
@Validated
public class EnrollmentController {

    private final StudentBookEnrollmentRepository enrollmentRepository;
    private final UserRepository userRepository;
    private final WordBookRepository wordBookRepository;

    public EnrollmentController(StudentBookEnrollmentRepository enrollmentRepository,
                                UserRepository userRepository,
                                WordBookRepository wordBookRepository) {
        this.enrollmentRepository = enrollmentRepository;
        this.userRepository = userRepository;
        this.wordBookRepository = wordBookRepository;
    }

    @PostMapping
    public ApiResponse<StudentBookEnrollment> create(@Valid @RequestBody EnrollmentCreateRequest req) {
        // 可选：校验用户与词书存在
        userRepository.findById(req.getStudentId())
                .orElseThrow(() -> new IllegalArgumentException("student not found"));
        wordBookRepository.findById(req.getBookId())
                .orElseThrow(() -> new IllegalArgumentException("word book not found"));

        return enrollmentRepository.findByStudentIdAndBookId(req.getStudentId(), req.getBookId())
                .map(ApiResponse::success)
                .orElseGet(() -> {
                    StudentBookEnrollment e = new StudentBookEnrollment();
                    e.setStudentId(req.getStudentId());
                    e.setBookId(req.getBookId());
                    e.setStartDate(req.getStartDate() != null ? req.getStartDate() : LocalDate.now());
                    e.setLearnedCount(0);
                    return ApiResponse.success(enrollmentRepository.save(e));
                });
    }

    @GetMapping("/student/{studentId}")
    public ApiResponse<List<StudentBookEnrollment>> listByStudent(@PathVariable Long studentId) {
        return ApiResponse.success(enrollmentRepository.findByStudentId(studentId));
    }
}
