package com.slab.words.controller;

import com.slab.words.common.ApiResponse;
import com.slab.words.domain.User;
import com.slab.words.domain.enums.Role;
import com.slab.words.domain.enums.UserStatus;
import com.slab.words.dto.AuthLoginRequest;
import com.slab.words.dto.AuthRegisterRequest;
import com.slab.words.dto.AuthResponse;
import com.slab.words.repository.UserRepository;
import com.slab.words.security.JwtUtil;
import com.slab.words.dto.MeResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/auth")
@Validated
public class AuthController {

    private final AuthenticationManager authenticationManager;
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;

    @Value("${app.security.jwt.access-token-exp-minutes:30}")
    private long accessTokenExpMinutes;

    public AuthController(AuthenticationManager authenticationManager,
                          UserRepository userRepository,
                          PasswordEncoder passwordEncoder,
                          JwtUtil jwtUtil) {
        this.authenticationManager = authenticationManager;
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
        this.jwtUtil = jwtUtil;
    }

    @PostMapping("/register")
    public ApiResponse<AuthResponse> register(@Valid @RequestBody AuthRegisterRequest req) {
        Optional<User> existed = userRepository.findByUsername(req.getUsername());
        if (existed.isPresent()) {
            return ApiResponse.failure(HttpStatus.CONFLICT.value(), "Username already exists", null);
        }
        Role role = req.getRole() == null ? Role.STUDENT : req.getRole();
        User user = User.builder()
                .username(req.getUsername())
                .email(req.getEmail())
                .phone(req.getPhone())
                .realName(req.getRealName())
                .passwordHash(passwordEncoder.encode(req.getPassword()))
                .role(role)
                .status(UserStatus.ACTIVE)
                .build();
        userRepository.save(user);

        Map<String, Object> claims = new HashMap<>();
        claims.put("role", user.getRole().name());
        claims.put("uid", user.getId());
        String token = jwtUtil.generateToken(user.getUsername(), claims);
        AuthResponse resp = new AuthResponse(token, "Bearer", accessTokenExpMinutes * 60, user.getUsername(), user.getRole(), user.getRealName());
        return ApiResponse.success(resp);
    }

    @PostMapping("/login")
    public ApiResponse<AuthResponse> login(@Valid @RequestBody AuthLoginRequest req) {
        try {
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(req.getUsername(), req.getPassword())
            );
        } catch (BadCredentialsException e) {
            return ApiResponse.failure(HttpStatus.UNAUTHORIZED.value(), "Invalid username or password", null);
        } catch (AuthenticationException e) {
            return ApiResponse.failure(HttpStatus.UNAUTHORIZED.value(), e.getMessage(), null);
        }
        User user = userRepository.findByUsername(req.getUsername()).orElseThrow();
        Map<String, Object> claims = new HashMap<>();
        claims.put("role", user.getRole().name());
        claims.put("uid", user.getId());
        String token = jwtUtil.generateToken(user.getUsername(), claims);
        AuthResponse resp = new AuthResponse(token, "Bearer", accessTokenExpMinutes * 60, user.getUsername(), user.getRole(), user.getRealName());
        return ApiResponse.success(resp);
    }

    @GetMapping("/me")
    public ApiResponse<MeResponse> me(@AuthenticationPrincipal UserDetails principal) {
        if (principal == null) {
            return ApiResponse.failure(HttpStatus.UNAUTHORIZED.value(), "Unauthorized", null);
        }
        User user = userRepository.findByUsername(principal.getUsername()).orElseThrow();
        MeResponse me = new MeResponse(user.getId(), user.getUsername(), user.getRole(), user.getRealName());
        return ApiResponse.success(me);
    }
}
