package com.slab.words.controller;

import com.slab.words.common.ApiResponse;
import com.slab.words.domain.TeacherCode;
import com.slab.words.domain.User;
import com.slab.words.domain.StudentInviteCode;
import com.slab.words.domain.StudentBookEnrollment;
import com.slab.words.domain.WordBook;
import com.slab.words.domain.enums.Role;
import com.slab.words.domain.enums.UserStatus;
import com.slab.words.dto.PageResponse;
import com.slab.words.dto.StudentSummary;
import com.slab.words.dto.TeacherCodeResponse;
import com.slab.words.dto.StudentPrebuiltCreateRequest;
import com.slab.words.dto.StudentInviteCodeResponse;
import com.slab.words.dto.StudentEnrollmentUpsertRequest;
import com.slab.words.repository.TeacherCodeRepository;
import com.slab.words.repository.TeacherStudentBindingRepository;
import com.slab.words.repository.UserRepository;
import com.slab.words.repository.StudentInviteCodeRepository;
import com.slab.words.repository.StudentBookEnrollmentRepository;
import com.slab.words.repository.WordBookRepository;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.security.SecureRandom;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/teachers/me")
@PreAuthorize("hasRole('TEACHER')")
public class TeacherController {

    private final TeacherCodeRepository teacherCodeRepository;
    private final TeacherStudentBindingRepository bindingRepository;
    private final UserRepository userRepository;
    private final StudentInviteCodeRepository studentInviteCodeRepository;
    private final StudentBookEnrollmentRepository enrollmentRepository;
    private final WordBookRepository wordBookRepository;
    private final PasswordEncoder passwordEncoder;
    private static final String CODE_ALPHABET = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789";
    private static final SecureRandom RANDOM = new SecureRandom();

    public TeacherController(TeacherCodeRepository teacherCodeRepository,
                             TeacherStudentBindingRepository bindingRepository,
                             UserRepository userRepository,
                             StudentInviteCodeRepository studentInviteCodeRepository,
                             StudentBookEnrollmentRepository enrollmentRepository,
                             WordBookRepository wordBookRepository,
                             PasswordEncoder passwordEncoder) {
        this.teacherCodeRepository = teacherCodeRepository;
        this.bindingRepository = bindingRepository;
        this.userRepository = userRepository;
        this.studentInviteCodeRepository = studentInviteCodeRepository;
        this.enrollmentRepository = enrollmentRepository;
        this.wordBookRepository = wordBookRepository;
        this.passwordEncoder = passwordEncoder;
    }

    @GetMapping("/code")
    public ApiResponse<TeacherCodeResponse> getMyCode(@AuthenticationPrincipal UserDetails principal) {
        // 老师专属码已废弃
        return ApiResponse.failure(410, "Teacher code deprecated. Use student invite codes.", null);
    }

    @PostMapping("/code/rotate")
    public ApiResponse<TeacherCodeResponse> rotateMyCode(@AuthenticationPrincipal UserDetails principal) {
        // 老师专属码已废弃
        return ApiResponse.failure(410, "Teacher code deprecated. Use student invite codes.", null);
    }

    // 预建学生并生成一次性激活码（若已有未使用邀请码则直接返回，不新建）
    @PostMapping("/students/prebuilt")
    public ApiResponse<StudentInviteCodeResponse> createPrebuiltStudent(
            @AuthenticationPrincipal UserDetails principal,
            @RequestBody StudentPrebuiltCreateRequest req) {
        if (principal == null) return ApiResponse.failure(HttpStatus.UNAUTHORIZED.value(), "Unauthorized", null);
        User me = userRepository.findByUsername(principal.getUsername()).orElseThrow();

        String username = (req.getUsername() != null && !req.getUsername().isBlank())
                ? req.getUsername().trim()
                : generateStubUsername(me.getId());
        // 确保唯一
        int guard = 0;
        while (guard < 50 && userRepository.findByUsername(username).isPresent()) {
            username = generateStubUsername(me.getId());
            guard++;
        }

        String rawTempPwd = randomCode(12);
        User student = User.builder()
                .username(username)
                .realName(req.getRealName())
                .passwordHash(passwordEncoder.encode(rawTempPwd))
                .role(Role.STUDENT)
                .status(UserStatus.ACTIVE)
                .build();
        // 使用默认 role=STUDENT, status=ACTIVE
        userRepository.save(student);

        if (!bindingRepository.existsByTeacherIdAndStudentId(me.getId(), student.getId())) {
            var b = com.slab.words.domain.TeacherStudentBinding.builder()
                    .teacherId(me.getId())
                    .studentId(student.getId())
                    .build();
            bindingRepository.save(b);
        }

        var existed = studentInviteCodeRepository.findByStudentId(student.getId());
        if (existed.isPresent() && !existed.get().isUsed()) {
            return ApiResponse.success(new StudentInviteCodeResponse(student.getId(), existed.get().getCode()));
        }

        String code = generateUniqueInviteCode(8);
        StudentInviteCode sic = StudentInviteCode.builder()
                .teacherId(me.getId())
                .studentId(student.getId())
                .code(code)
                .used(false)
                .build();
        studentInviteCodeRepository.save(sic);
        return ApiResponse.success(new StudentInviteCodeResponse(student.getId(), code));
    }

    // 查询学生的一次性激活码（若已使用则返回 410）
    @GetMapping("/students/{studentId}/invite-code")
    public ApiResponse<StudentInviteCodeResponse> getStudentInviteCode(
            @AuthenticationPrincipal UserDetails principal,
            @PathVariable Long studentId) {
        if (principal == null) return ApiResponse.failure(HttpStatus.UNAUTHORIZED.value(), "Unauthorized", null);
        User me = userRepository.findByUsername(principal.getUsername()).orElseThrow();
        if (!bindingRepository.existsByTeacherIdAndStudentId(me.getId(), studentId)) {
            return ApiResponse.failure(HttpStatus.NOT_FOUND.value(), "Student not found under this teacher", null);
        }
        var sicOpt = studentInviteCodeRepository.findByStudentId(studentId);
        if (sicOpt.isEmpty()) {
            String code = generateUniqueInviteCode(8);
            StudentInviteCode sic = StudentInviteCode.builder()
                    .teacherId(me.getId())
                    .studentId(studentId)
                    .code(code)
                    .used(false)
                    .build();
            studentInviteCodeRepository.save(sic);
            return ApiResponse.success(new StudentInviteCodeResponse(studentId, code));
        }
        var sic = sicOpt.get();
        if (sic.isUsed()) {
            return ApiResponse.failure(410, "Invite code already used", null);
        }
        return ApiResponse.success(new StudentInviteCodeResponse(studentId, sic.getCode()));
    }

    @GetMapping("/students")
    public ApiResponse<Map<String, Object>> getMyStudents(
            @AuthenticationPrincipal UserDetails principal,
            @RequestParam(defaultValue = "1") @Min(1) int page,
            @RequestParam(defaultValue = "10") @Min(1) int size,
            @RequestParam(defaultValue = "") String q
    ) {
        if (principal == null) return ApiResponse.failure(HttpStatus.UNAUTHORIZED.value(), "Unauthorized", null);
        User me = userRepository.findByUsername(principal.getUsername()).orElseThrow();
        Pageable pageable = PageRequest.of(page - 1, size);

        // 找出该老师绑定的所有学生ID，并移除“已登录学生通过邀请码绑定”造成的重复绑定
        var allBindings = bindingRepository.findByTeacherId(me.getId(), PageRequest.of(0, Integer.MAX_VALUE));
        List<Long> allStudentIds = allBindings.getContent()
                .stream()
                .filter(b -> {
                    // 若存在该学生作为 usedBy 的已使用邀请码且归属同一老师，则认为这是“真实学生账号”的重复绑定，隐藏之
                    var inviteOpt = studentInviteCodeRepository.findTopByUsedByOrderByUsedAtDesc(b.getStudentId());
                    return inviteOpt.isEmpty() || !inviteOpt.get().isUsed() || !Objects.equals(inviteOpt.get().getTeacherId(), me.getId());
                })
                .map(b -> b.getStudentId())
                .toList();
        if (allStudentIds.isEmpty()) {
            Map<String, Object> resp = new HashMap<>();
            resp.put("items", Collections.emptyList());
            resp.put("total", 0);
            resp.put("page", page);
            resp.put("size", size);
            return ApiResponse.success(resp);
        }

        List<Long> filteredIds;
        if (q != null && !q.trim().isEmpty()) {
            String keyword = q.trim();
            filteredIds = userRepository.searchByIdsAndNameLike(allStudentIds, keyword)
                    .stream().map(User::getId).toList();
            if (filteredIds.isEmpty()) {
                Map<String, Object> resp = new HashMap<>();
                resp.put("items", Collections.emptyList());
                resp.put("total", 0);
                resp.put("page", page);
                resp.put("size", size);
                return ApiResponse.success(resp);
            }
        } else {
            filteredIds = allStudentIds;
        }

        Page<Long> pagedIds = toPage(filteredIds, pageable);
        List<User> students = filteredIds.isEmpty() ? Collections.emptyList() : userRepository.findByIdIn(pagedIds.getContent());

        List<StudentSummary> items = students.stream().map(u -> {
            // 展示名称：优先显示真实姓名，缺省回退到用户名
            String displayName = (u.getRealName() != null && !u.getRealName().isBlank())
                    ? u.getRealName().trim()
                    : u.getUsername();

            // 选课与词书信息
            String bookName = "未设置词书";
            int totalLessons = 0;
            int progress = 0;
            int completedLessons = 0;
            var enrollmentOpt = enrollmentRepository.findTopByStudentIdOrderByCreatedAtDesc(u.getId());
            if (enrollmentOpt.isPresent()) {
                StudentBookEnrollment enr = enrollmentOpt.get();
                totalLessons = enr.getTotalLessons() == null ? 0 : enr.getTotalLessons();
                if (enr.getBookId() != null) {
                    WordBook wb = wordBookRepository.findById(enr.getBookId()).orElse(null);
                    if (wb != null) {
                        bookName = wb.getName();
                        Integer learned = enr.getLearnedCount() == null ? 0 : enr.getLearnedCount();
                        Integer totalWords = wb.getTotalWords() == null ? 0 : wb.getTotalWords();
                        if (totalWords != null && totalWords > 0) {
                            progress = Math.min(100, Math.max(0, (int)Math.round(learned * 100.0 / totalWords)));
                            if (totalLessons > 0) {
                                double avgPerLesson = totalWords / (double) totalLessons;
                                completedLessons = (int)Math.floor(learned / Math.max(1.0, avgPerLesson));
                                if (completedLessons > totalLessons) completedLessons = totalLessons;
                            }
                        }
                    }
                }
            }

            // 检查该学生是否已激活（激活码已使用）
            boolean activated = studentInviteCodeRepository.findByStudentId(u.getId())
                    .map(StudentInviteCode::isUsed)
                    .orElse(false);
            return new StudentSummary(
                    u.getId(),
                    displayName,
                    String.valueOf(u.getId()),
                    bookName,
                    progress,
                    completedLessons,
                    totalLessons == 0 ? 20 : totalLessons,
                    "今天",
                    activated
            );
        }).collect(Collectors.toList());

        Map<String, Object> resp = new HashMap<>();
        resp.put("items", items);
        resp.put("total", filteredIds.size());
        resp.put("page", page);
        resp.put("size", size);
        return ApiResponse.success(resp);
    }

    @GetMapping("/students/{studentId}")
    public ApiResponse<Map<String, Object>> getStudentDetails(
            @AuthenticationPrincipal UserDetails principal,
            @PathVariable Long studentId
    ) {
        User me = userRepository.findByUsername(principal.getUsername()).orElseThrow();
        if (!bindingRepository.existsByTeacherIdAndStudentId(me.getId(), studentId)) {
            return ApiResponse.failure(HttpStatus.NOT_FOUND.value(), "Student not found under this teacher", null);
        }
        
        // 若该预建学生的邀请码已被使用且存在 usedBy，则优先返回 usedBy 学生填写的信息（用于弹窗展示）
        var inviteOpt = studentInviteCodeRepository.findByStudentId(studentId);
        User base = userRepository.findById(studentId).orElseThrow();
        User infoSource = base;
        if (inviteOpt.isPresent() && inviteOpt.get().isUsed() && inviteOpt.get().getUsedBy() != null) {
            var usedByUser = userRepository.findById(inviteOpt.get().getUsedBy()).orElse(null);
            if (usedByUser != null) infoSource = usedByUser;
        }

        Map<String, Object> details = new HashMap<>();
        details.put("id", base.getId()); // 仍返回预建学生ID以对应列表项
        details.put("username", infoSource.getUsername());
        details.put("realName", infoSource.getRealName());
        details.put("phone", infoSource.getPhone());
        details.put("role", base.getRole().name());
        
        return ApiResponse.success(details);
    }

    // 教师删除学生（彻底删除学生账号及其关联数据）
    @DeleteMapping("/students/{studentId}")
    public ApiResponse<Void> deleteStudent(
            @AuthenticationPrincipal UserDetails principal,
            @PathVariable Long studentId
    ) {
        if (principal == null) return ApiResponse.failure(HttpStatus.UNAUTHORIZED.value(), "Unauthorized", null);
        User me = userRepository.findByUsername(principal.getUsername()).orElseThrow();
        if (!bindingRepository.existsByTeacherIdAndStudentId(me.getId(), studentId)) {
            return ApiResponse.failure(HttpStatus.NOT_FOUND.value(), "Student not found under this teacher", null);
        }
        // 删除用户记录，依赖外键约束进行级联删除绑定与激活码
        if (userRepository.existsById(studentId)) {
            userRepository.deleteById(studentId);
        }
        return ApiResponse.success(null);
    }

    // 设置/更新学生选课（词书 + 总课时）
    @PutMapping("/students/{studentId}/enrollment")
    public ApiResponse<Map<String, Object>> upsertEnrollment(
            @AuthenticationPrincipal UserDetails principal,
            @PathVariable Long studentId,
            @Valid @RequestBody StudentEnrollmentUpsertRequest req
    ) {
        if (principal == null) return ApiResponse.failure(HttpStatus.UNAUTHORIZED.value(), "Unauthorized", null);
        User me = userRepository.findByUsername(principal.getUsername()).orElseThrow();
        if (!bindingRepository.existsByTeacherIdAndStudentId(me.getId(), studentId)) {
            return ApiResponse.failure(HttpStatus.NOT_FOUND.value(), "Student not found under this teacher", null);
        }

        WordBook wb = wordBookRepository.findById(req.getBookId())
                .orElseThrow(() -> new IllegalArgumentException("word book not found"));

        StudentBookEnrollment enr = enrollmentRepository
                .findByStudentIdAndBookId(studentId, req.getBookId())
                .orElseGet(() -> {
                    StudentBookEnrollment e = new StudentBookEnrollment();
                    e.setStudentId(studentId);
                    e.setBookId(req.getBookId());
                    e.setStartDate(LocalDate.now());
                    e.setLearnedCount(0);
                    return e;
                });
        enr.setTotalLessons(req.getTotalLessons());
        enrollmentRepository.save(enr);

        Map<String, Object> data = new HashMap<>();
        data.put("studentId", studentId);
        data.put("bookId", wb.getId());
        data.put("bookName", wb.getName());
        data.put("totalLessons", req.getTotalLessons());
        data.put("totalWords", wb.getTotalWords());
        return ApiResponse.success(data);
    }

    // 获取学生当前选课信息
    @GetMapping("/students/{studentId}/enrollment")
    public ApiResponse<Map<String, Object>> getEnrollment(
            @AuthenticationPrincipal UserDetails principal,
            @PathVariable Long studentId
    ) {
        if (principal == null) return ApiResponse.failure(HttpStatus.UNAUTHORIZED.value(), "Unauthorized", null);
        User me = userRepository.findByUsername(principal.getUsername()).orElseThrow();
        if (!bindingRepository.existsByTeacherIdAndStudentId(me.getId(), studentId)) {
            return ApiResponse.failure(HttpStatus.NOT_FOUND.value(), "Student not found under this teacher", null);
        }

        var opt = enrollmentRepository.findTopByStudentIdOrderByCreatedAtDesc(studentId);
        if (opt.isEmpty()) {
            return ApiResponse.success(null);
        }
        StudentBookEnrollment enr = opt.get();
        WordBook wb = enr.getBookId() == null ? null : wordBookRepository.findById(enr.getBookId()).orElse(null);
        Map<String, Object> data = new HashMap<>();
        data.put("studentId", studentId);
        data.put("bookId", enr.getBookId());
        data.put("bookName", wb == null ? null : wb.getName());
        data.put("totalLessons", enr.getTotalLessons());
        data.put("totalWords", wb == null ? null : wb.getTotalWords());
        return ApiResponse.success(data);
    }

    private String generateUniqueInviteCode(int len) {
        String code;
        int guard = 0;
        do {
            code = randomCode(len);
            guard++;
        } while (guard < 50 && studentInviteCodeRepository.existsByCode(code));
        return code;
    }

    private static String randomCode(int len) {
        StringBuilder sb = new StringBuilder(len);
        for (int i = 0; i < len; i++) {
            sb.append(CODE_ALPHABET.charAt(RANDOM.nextInt(CODE_ALPHABET.length())));
        }
        return sb.toString();
    }

    private String generateStubUsername(Long teacherId) {
        return "stub_" + teacherId + "_" + randomCode(6);
    }

    private static <T> Page<T> toPage(List<T> list, Pageable pageable) {
        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), list.size());
        if (start > end) start = end;
        List<T> content = list.subList(start, end);
        return new PageImpl<>(content, pageable, list.size());
    }
}
