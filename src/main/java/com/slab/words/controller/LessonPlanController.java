package com.slab.words.controller;

import com.slab.words.common.ApiResponse;
import com.slab.words.domain.LessonPlan;
import com.slab.words.dto.LessonPlanUpsertRequest;
import com.slab.words.repository.LessonPlanRepository;
import com.slab.words.repository.UserRepository;
import com.slab.words.repository.WordBookRepository;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@RestController
@RequestMapping("/lesson-plans")
@Validated
public class LessonPlanController {

    private final LessonPlanRepository lessonPlanRepository;
    private final UserRepository userRepository;
    private final WordBookRepository wordBookRepository;

    public LessonPlanController(LessonPlanRepository lessonPlanRepository,
                                UserRepository userRepository,
                                WordBookRepository wordBookRepository) {
        this.lessonPlanRepository = lessonPlanRepository;
        this.userRepository = userRepository;
        this.wordBookRepository = wordBookRepository;
    }

    @PutMapping
    public ApiResponse<LessonPlan> upsert(@Valid @RequestBody LessonPlanUpsertRequest req) {
        userRepository.findById(req.getStudentId())
                .orElseThrow(() -> new IllegalArgumentException("student not found"));
        wordBookRepository.findById(req.getBookId())
                .orElseThrow(() -> new IllegalArgumentException("word book not found"));

        Optional<LessonPlan> existing = lessonPlanRepository.findByStudentIdAndBookId(req.getStudentId(), req.getBookId());
        LessonPlan plan = existing.orElseGet(LessonPlan::new);
        plan.setStudentId(req.getStudentId());
        plan.setBookId(req.getBookId());
        plan.setDailyNewWords(req.getDailyNewWords());
        plan.setDailyReviewWords(req.getDailyReviewWords());
        plan.setEbbinghausPlan(req.getEbbinghausPlan());
        return ApiResponse.success(lessonPlanRepository.save(plan));
    }

    @GetMapping
    public ApiResponse<LessonPlan> getOne(@RequestParam Long studentId, @RequestParam Long bookId) {
        return lessonPlanRepository.findByStudentIdAndBookId(studentId, bookId)
                .map(ApiResponse::success)
                .orElseGet(() -> ApiResponse.success(null));
    }
}
