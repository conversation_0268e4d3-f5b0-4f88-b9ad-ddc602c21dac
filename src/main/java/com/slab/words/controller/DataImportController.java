package com.slab.words.controller;

import com.slab.words.common.ApiResponse;
import com.slab.words.service.DataImportService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/data-import")
@RequiredArgsConstructor
public class DataImportController {

    private final DataImportService dataImportService;

    @PostMapping("/wordbook")
    public ApiResponse<String> importWordBook(
            @RequestParam("name") String name,
            @RequestParam("stage") String stage,
            @RequestParam("description") String description,
            @RequestParam("file") MultipartFile file) {
        try {
            String result = dataImportService.importWordBook(name, stage, description, file);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.failure(500, "导入失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/wordbook/batch")
    public ApiResponse<String> importWordBookBatch(
            @RequestParam("name") String name,
            @RequestParam("stage") String stage,
            @RequestParam("description") String description,
            @RequestParam("file") MultipartFile file) {
        try {
            String result = dataImportService.importWordBookBatch(name, stage, description, file);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.failure(500, "批量导入失败: " + e.getMessage(), null);
        }
    }

    @GetMapping("/progress")
    public ApiResponse<DataImportService.ImportProgress> getProgress() {
        return ApiResponse.success(dataImportService.getProgress());
    }

    @DeleteMapping("/wordbook/{name}")
    public ApiResponse<String> deleteWordBook(@PathVariable("name") String name) {
        try {
            String result = dataImportService.deleteWordBook(name);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.failure(500, "删除失败: " + e.getMessage(), null);
        }
    }
}
