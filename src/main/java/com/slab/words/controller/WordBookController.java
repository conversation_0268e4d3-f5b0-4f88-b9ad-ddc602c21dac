package com.slab.words.controller;

import com.slab.words.common.ApiResponse;
import com.slab.words.domain.Word;
import com.slab.words.domain.WordBook;
import com.slab.words.domain.enums.Stage;
import com.slab.words.dto.PageResponse;
import com.slab.words.repository.WordBookRepository;
import com.slab.words.repository.WordRepository;
import jakarta.validation.constraints.Min;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/word-books")
@Validated
public class WordBookController {

    private final WordBookRepository wordBookRepository;
    private final WordRepository wordRepository;

    public WordBookController(WordBookRepository wordBookRepository, WordRepository wordRepository) {
        this.wordBookRepository = wordBookRepository;
        this.wordRepository = wordRepository;
    }

    @GetMapping
    public ApiResponse<PageResponse<WordBook>> list(
            @RequestParam(value = "stage", required = false) Stage stage,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "page", defaultValue = "0") @Min(0) int page,
            @RequestParam(value = "size", defaultValue = "20") @Min(1) int size
    ) {
        PageRequest pr = PageRequest.of(page, size);
        Page<WordBook> result;
        if (stage != null && name != null && !name.isBlank()) {
            result = wordBookRepository.findByStageAndNameContainingIgnoreCase(stage, name, pr);
        } else if (stage != null) {
            result = wordBookRepository.findByStage(stage, pr);
        } else if (name != null && !name.isBlank()) {
            result = wordBookRepository.findByNameContainingIgnoreCase(name, pr);
        } else {
            result = wordBookRepository.findAll(pr);
        }
        return ApiResponse.success(PageResponse.from(result));
    }

    @GetMapping("/{id}/words")
    public ApiResponse<PageResponse<Word>> words(
            @PathVariable("id") Long bookId,
            @RequestParam(value = "q", required = false) String q,
            @RequestParam(value = "page", defaultValue = "0") @Min(0) int page,
            @RequestParam(value = "size", defaultValue = "20") @Min(1) int size
    ) {
        PageRequest pr = PageRequest.of(page, size);
        Page<Word> result;
        if (q != null && !q.isBlank()) {
            result = wordRepository.findByBookIdAndHeadwordContainingIgnoreCase(bookId, q, pr);
        } else {
            result = wordRepository.findByBookId(bookId, pr);
        }
        return ApiResponse.success(PageResponse.from(result));
    }
}
