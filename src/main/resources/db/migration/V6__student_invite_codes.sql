-- V6: student invite codes (one-time, non-rotatable)
SET NAMES utf8mb4;

CREATE TABLE IF NOT EXISTS student_invite_codes (
    id          BIGINT PRIMARY KEY AUTO_INCREMENT,
    teacher_id  BIGINT NOT NULL,
    student_id  BIGINT NOT NULL,
    code        VARCHAR(16) NOT NULL,
    used        TINYINT(1) NOT NULL DEFAULT 0,
    used_by     BIGINT NULL,
    used_at     TIMESTAMP NULL,
    created_at  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uq_invite_code (code),
    UNIQUE KEY uq_invite_student (student_id),
    KEY idx_invite_teacher (teacher_id),
    KEY idx_invite_student (student_id),
    KEY idx_invite_used_by (used_by),
    CONSTRAINT fk_invite_teacher FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT fk_invite_student FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT fk_invite_used_by FOREIGN KEY (used_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Notes:
-- 1) code: 一次性使用，服务端在使用时将 used=1, used_by=<userId>, used_at=NOW()，防止重复激活
-- 2) student_id: 每个学生仅允许存在一条当前有效邀请码（不可轮换），如需更换，应先删除旧记录再新建
-- 3) teacher_id, student_id 均指向 users(id)，便于审计与查询
