-- V2: teacher codes and teacher-student bindings
SET NAMES utf8mb4;

CREATE TABLE IF NOT EXISTS teacher_codes (
    id          BIGINT PRIMARY KEY AUTO_INCREMENT,
    teacher_id  BIGINT NOT NULL,
    code        VARCHAR(16) NOT NULL,
    rotated_at  TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    created_at  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uq_teacher_codes_teacher (teacher_id),
    UNIQUE KEY uq_teacher_codes_code (code),
    KEY idx_teacher_codes_teacher (teacher_id),
    CONSTRAINT fk_teacher_codes_teacher FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS teacher_student_bindings (
    id          BIGINT PRIMARY KEY AUTO_INCREMENT,
    teacher_id  BIGINT NOT NULL,
    student_id  BIGINT NOT NULL,
    created_at  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uq_binding_teacher_student (teacher_id, student_id),
    UNIQUE KEY uq_binding_student_unique (student_id),
    KEY idx_bindings_teacher (teacher_id),
    KEY idx_bindings_student (student_id),
    CONSTRAINT fk_bind_teacher FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT fk_bind_student FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
