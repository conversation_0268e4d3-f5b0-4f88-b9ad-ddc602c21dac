-- Create dictionary_entries table for ECDICT full dictionary
CREATE TABLE IF NOT EXISTS dictionary_entries (
  id BIGINT NOT NULL AUTO_INCREMENT,
  word VARCHAR(128) NOT NULL,
  sw VARCHAR(128) NOT NULL,
  phonetic VARCHAR(64) NULL,
  definition MEDIUMTEXT NULL,
  translation MEDIUMTEXT NULL,
  pos VARCHAR(128) NULL,
  collins TINYINT NULL,
  oxford TINYINT NULL,
  tag VARCHAR(512) NULL,
  bnc INT NULL,
  frq INT NULL,
  exchange TEXT NULL,
  detail JSON NULL,
  audio_url VARCHAR(255) NULL,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY uk_word (word),
  KEY idx_sw (sw),
  KEY idx_frq (frq),
  KEY idx_bnc (bnc)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
