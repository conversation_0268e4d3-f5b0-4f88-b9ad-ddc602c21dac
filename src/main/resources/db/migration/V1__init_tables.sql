-- V1: initial tables for words-learning
-- Charset and engine
SET NAMES utf8mb4;

-- ENUM mapping for stage:
-- PRESCHOOL(幼儿), PRIMARY(小学), JUNIOR(初中), SENIOR(高中), UNIVERSITY(大学), ADULT(成人)

CREATE TABLE IF NOT EXISTS users (
    id              BIGINT PRIMARY KEY AUTO_INCREMENT,
    username        VARCHAR(50)  NOT NULL,
    email           VARCHAR(100) NULL,
    phone           VARCHAR(20)  NULL,
    password_hash   VARCHAR(255) NOT NULL,
    role            ENUM('ADMIN','TEACHER','STUDENT') NOT NULL DEFAULT 'STUDENT',
    stage           ENUM('PRESCHOOL','PRIMARY','JUNIOR','SENIOR','UNIVERSITY','ADULT') NULL COMMENT '仅学生使用的阶段',
    status          ENUM('ACTIVE','DISABLED') NOT NULL DEFAULT 'ACTIVE',
    created_at      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uq_users_username (username),
    UNIQUE KEY uq_users_email (email),
    UNIQUE KEY uq_users_phone (phone),
    KEY idx_users_role (role),
    KEY idx_users_stage (stage)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS word_books (
    id              BIGINT PRIMARY KEY AUTO_INCREMENT,
    name            VARCHAR(100) NOT NULL,
    language        VARCHAR(20)  NOT NULL DEFAULT 'en',
    stage           ENUM('PRESCHOOL','PRIMARY','JUNIOR','SENIOR','UNIVERSITY','ADULT') NULL COMMENT '目标适配阶段',
    description     TEXT         NULL,
    total_words     INT UNSIGNED NOT NULL DEFAULT 0,
    created_at      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uq_word_books_name (name),
    KEY idx_word_books_stage (stage)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS words (
    id          BIGINT PRIMARY KEY AUTO_INCREMENT,
    book_id     BIGINT       NOT NULL,
    headword    VARCHAR(128) NOT NULL,
    phonetic    VARCHAR(64)  NULL,
    pos         VARCHAR(16)  NULL,
    meaning     JSON         NULL,
    audio_url   VARCHAR(255) NULL,
    frequency   INT UNSIGNED NULL,
    created_at  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uq_words_book_headword (book_id, headword),
    KEY idx_words_headword (headword),
    CONSTRAINT fk_words_book FOREIGN KEY (book_id) REFERENCES word_books(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS student_book_enrollments (
    id              BIGINT PRIMARY KEY AUTO_INCREMENT,
    student_id      BIGINT NOT NULL,
    book_id         BIGINT NOT NULL,
    start_date      DATE   NULL,
    end_date        DATE   NULL,
    learned_count   INT UNSIGNED NOT NULL DEFAULT 0,
    review_due_date DATE   NULL,
    created_at      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uq_enrollment_student_book (student_id, book_id),
    KEY idx_enrollment_review_due_date (review_due_date),
    CONSTRAINT fk_enroll_student FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT fk_enroll_book    FOREIGN KEY (book_id)    REFERENCES word_books(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS lesson_plans (
    id                  BIGINT PRIMARY KEY AUTO_INCREMENT,
    student_id          BIGINT NOT NULL,
    book_id             BIGINT NOT NULL,
    daily_new_words     INT UNSIGNED NOT NULL DEFAULT 20,
    daily_review_words  INT UNSIGNED NOT NULL DEFAULT 50,
    ebbinghaus_plan     JSON NULL,
    created_at          TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at          TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uq_plan_student_book (student_id, book_id),
    CONSTRAINT fk_plan_student FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT fk_plan_book    FOREIGN KEY (book_id)    REFERENCES word_books(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
