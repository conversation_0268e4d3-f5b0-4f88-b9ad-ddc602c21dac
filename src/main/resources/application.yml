server:
  port: 8080
  servlet:
    context-path: /api/v1

spring:
  application:
    name: words

  datasource:
    url: jdbc:mysql://${DB_HOST:127.0.0.1}:${DB_PORT:3306}/${DB_NAME:words_learning}?useUnicode=true&characterEncoding=utf-8&serverTimezone=Asia/Shanghai&useSSL=false&allowPublicKeyRetrieval=true
    username: ${DB_USER:root}
    password: ${DB_PASSWORD:root}
    driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    hibernate:
      ddl-auto: none
    open-in-view: false
    properties:
      hibernate:
        format_sql: true
    show-sql: false

  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    table: flyway_schema_history

management:
  endpoints:
    web:
      exposure:
        include: health,info

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    path: /swagger-ui.html

app:
  cors:
    allowed-origins: "*"
    allowed-methods: "*"
    allowed-headers: "*"
  security:
    jwt:
      # 请在生产环境使用更安全且足够长度的密钥，可通过环境变量 JWT_SECRET 注入
      secret: ${JWT_SECRET:change-me-please-256-bit-secret-change-me-please}
      issuer: words-learning
      access-token-exp-minutes: 30
